// Token Debugging Utility
require('dotenv').config();
const mysql = require('mysql2/promise');

async function debugTokens() {
  let connection;
  
  try {
    console.log('🔍 TOKEN DEBUGGING UTILITY');
    console.log('='.repeat(50));
    
    // Create database connection
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'sports_website'
    });

    console.log('✅ Connected to database');
    
    // Get all users with reset tokens
    const [users] = await connection.query(`
      SELECT 
        id, 
        username, 
        email, 
        reset_token,
        reset_token_expiry,
        CASE 
          WHEN reset_token_expiry > NOW() THEN 'VALID'
          WHEN reset_token_expiry <= NOW() THEN 'EXPIRED'
          WHEN reset_token_expiry IS NULL THEN 'NO_TOKEN'
        END as token_status,
        TIMESTAMPDIFF(MINUTE, NOW(), reset_token_expiry) as minutes_remaining
      FROM users 
      WHERE reset_token IS NOT NULL
      ORDER BY reset_token_expiry DESC
    `);

    console.log(`\n📋 Found ${users.length} users with reset tokens:\n`);
    
    if (users.length === 0) {
      console.log('❌ No active reset tokens found');
      console.log('💡 Generate a reset token by using the forgot password feature');
      return;
    }

    users.forEach((user, index) => {
      console.log(`${index + 1}. User: ${user.username} (${user.email})`);
      console.log(`   Token: ${user.reset_token ? user.reset_token.substring(0, 16) + '...' : 'NULL'}`);
      console.log(`   Expiry: ${user.reset_token_expiry}`);
      console.log(`   Status: ${user.token_status}`);
      
      if (user.token_status === 'VALID') {
        console.log(`   ⏰ Time remaining: ${user.minutes_remaining} minutes`);
        console.log(`   🔗 Test link: http://localhost:3000/auth/reset-password?token=${user.reset_token}`);
      } else if (user.token_status === 'EXPIRED') {
        console.log(`   ⏰ Expired ${Math.abs(user.minutes_remaining)} minutes ago`);
      }
      
      console.log('');
    });

    // Check for recent token generation
    const [recentTokens] = await connection.query(`
      SELECT 
        username,
        email,
        reset_token_expiry,
        TIMESTAMPDIFF(MINUTE, reset_token_expiry, NOW()) as minutes_since_expiry
      FROM users 
      WHERE reset_token_expiry IS NOT NULL 
        AND reset_token_expiry > DATE_SUB(NOW(), INTERVAL 1 DAY)
      ORDER BY reset_token_expiry DESC
      LIMIT 5
    `);

    if (recentTokens.length > 0) {
      console.log('📅 Recent token activity (last 24 hours):');
      recentTokens.forEach((token, index) => {
        const status = token.minutes_since_expiry > 0 ? 'EXPIRED' : 'VALID';
        console.log(`   ${index + 1}. ${token.username} - ${status}`);
      });
      console.log('');
    }

    // Database time check
    const [dbTime] = await connection.query('SELECT NOW() as current_time');
    console.log(`🕐 Database current time: ${dbTime[0].current_time}`);
    console.log(`🕐 Server current time: ${new Date().toISOString()}`);
    
    // Timezone check
    const [timezone] = await connection.query('SELECT @@system_time_zone as timezone, @@time_zone as session_timezone');
    console.log(`🌍 Database timezone: ${timezone[0].timezone} (session: ${timezone[0].session_timezone})`);

  } catch (error) {
    console.error('❌ Error debugging tokens:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Function to clean expired tokens
async function cleanExpiredTokens() {
  let connection;
  
  try {
    console.log('\n🧹 CLEANING EXPIRED TOKENS');
    console.log('='.repeat(30));
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'sports_website'
    });

    const [result] = await connection.query(`
      UPDATE users 
      SET reset_token = NULL, reset_token_expiry = NULL 
      WHERE reset_token_expiry <= NOW()
    `);

    console.log(`✅ Cleaned ${result.affectedRows} expired tokens`);

  } catch (error) {
    console.error('❌ Error cleaning tokens:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Run based on command line argument
const command = process.argv[2];

if (command === 'clean') {
  cleanExpiredTokens();
} else {
  debugTokens();
}

console.log('\n💡 Usage:');
console.log('  node debug-tokens.js        - Show all tokens');
console.log('  node debug-tokens.js clean  - Clean expired tokens');
