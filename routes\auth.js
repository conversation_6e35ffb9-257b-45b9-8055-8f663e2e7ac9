const express = require('express');
const router = express.Router();
const bcrypt = require('bcrypt');
const crypto = require('crypto');
const db = require('../config/database');
const { sendPasswordResetEmail } = require('../services/emailService');

// Register page
router.get('/register', (req, res) => {
  if (req.session.user) {
    return res.redirect('/');
  }
  res.render('pages/auth/register', { 
    title: 'Register',
    error: null,
    redirect: req.query.redirect || '/'
  });
});

// Register user
router.post('/register', async (req, res) => {
  try {
    const { username, email, password, confirmPassword, fullName } = req.body;
    const redirect = req.body.redirect || '/';
    
    // Validate input
    if (!username || !email || !password || !confirmPassword) {
      return res.render('pages/auth/register', { 
        title: 'Register',
        error: 'All fields are required',
        redirect
      });
    }
    
    if (password !== confirmPassword) {
      return res.render('pages/auth/register', { 
        title: 'Register',
        error: 'Passwords do not match',
        redirect
      });
    }
    
    // Check if username or email already exists
    const [existingUser] = await db.query(
      'SELECT * FROM users WHERE username = ? OR email = ?',
      [username, email]
    );
    
    if (existingUser.length > 0) {
      return res.render('pages/auth/register', { 
        title: 'Register',
        error: 'Username or email already exists',
        redirect
      });
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    
    // Create user
    const [result] = await db.query(
      'INSERT INTO users (username, email, password, full_name) VALUES (?, ?, ?, ?)',
      [username, email, hashedPassword, fullName || null]
    );
    
    // Create session
    const user = {
      id: result.insertId,
      username,
      email,
      fullName: fullName || null
    };
    
    req.session.user = user;
    
    res.redirect(redirect);
  } catch (error) {
    console.error('Error registering user:', error);
    res.render('pages/auth/register', { 
      title: 'Register',
      error: 'Registration failed. Please try again.',
      redirect: req.body.redirect || '/'
    });
  }
});

// Login page
router.get('/login', (req, res) => {
  if (req.session.user) {
    return res.redirect('/');
  }
  res.render('pages/auth/login', { 
    title: 'Login',
    error: null,
    redirect: req.query.redirect || '/'
  });
});

// Login user
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    const redirect = req.body.redirect || '/';
    
    // Validate input
    if (!username || !password) {
      return res.render('pages/auth/login', { 
        title: 'Login',
        error: 'Username and password are required',
        redirect
      });
    }
    
    // Find user
    const [users] = await db.query(
      'SELECT * FROM users WHERE username = ? OR email = ?',
      [username, username]
    );
    
    if (users.length === 0) {
      return res.render('pages/auth/login', { 
        title: 'Login',
        error: 'Invalid username or password',
        redirect
      });
    }
    
    const user = users[0];
    
    // Check password
    const passwordMatch = await bcrypt.compare(password, user.password);
    
    if (!passwordMatch) {
      return res.render('pages/auth/login', { 
        title: 'Login',
        error: 'Invalid username or password',
        redirect
      });
    }
    
    // Create session
    req.session.user = {
      id: user.id,
      username: user.username,
      email: user.email,
      fullName: user.full_name
    };
    
    res.redirect(redirect);
  } catch (error) {
    console.error('Error logging in:', error);
    res.render('pages/auth/login', { 
      title: 'Login',
      error: 'Login failed. Please try again.',
      redirect: req.body.redirect || '/'
    });
  }
});

// Logout
router.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/');
});

// Forgot password page
router.get('/forgot-password', (req, res) => {
  if (req.session.user) {
    return res.redirect('/');
  }
  res.render('pages/auth/forgot-password', {
    title: 'Forgot Password',
    error: null,
    success: null
  });
});

// Send password reset email
router.post('/forgot-password', async (req, res) => {
  try {
    const { email } = req.body;

    // Validate input
    if (!email) {
      return res.render('pages/auth/forgot-password', {
        title: 'Forgot Password',
        error: 'Email address is required',
        success: null
      });
    }

    // Check if user exists
    const [users] = await db.query(
      'SELECT * FROM users WHERE email = ?',
      [email]
    );

    // Always show success message for security (don't reveal if email exists)
    const successMessage = 'If an account with that email exists, we have sent a password reset link.';

    if (users.length > 0) {
      const user = users[0];

      // Generate reset token with extended expiry for email delivery delays
      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetTokenExpiry = new Date(Date.now() + ********); // 24 hours from now (was 1 hour)

      // Store reset token in database
      await db.query(
        'UPDATE users SET reset_token = ?, reset_token_expiry = ? WHERE id = ?',
        [resetToken, resetTokenExpiry, user.id]
      );

      // Send password reset email with configurable URL
      const baseUrl = process.env.WEBSITE_URL || `${req.protocol}://${req.get('host')}`;
      const resetLink = `${baseUrl}/auth/reset-password?token=${resetToken}`;
      await sendPasswordResetEmail(email, resetLink);
    }

    res.render('pages/auth/forgot-password', {
      title: 'Forgot Password',
      error: null,
      success: successMessage
    });

  } catch (error) {
    console.error('Error in forgot password:', error);
    res.render('pages/auth/forgot-password', {
      title: 'Forgot Password',
      error: 'An error occurred. Please try again.',
      success: null
    });
  }
});

// Reset password page
router.get('/reset-password', async (req, res) => {
  try {
    const { token } = req.query;

    if (!token) {
      return res.redirect('/auth/forgot-password');
    }

    // Check if token exists first, then check expiry for better error messages
    const [allUsers] = await db.query(
      'SELECT *, reset_token_expiry FROM users WHERE reset_token = ?',
      [token]
    );

    if (allUsers.length === 0) {
      console.log(`❌ Invalid token attempted: ${token}`);
      return res.render('pages/auth/forgot-password', {
        title: 'Forgot Password',
        error: 'Invalid reset token. Please request a new password reset.',
        success: null
      });
    }

    const user = allUsers[0];
    const now = new Date();
    const expiry = new Date(user.reset_token_expiry);

    console.log(`🔍 Token check for user ${user.email}:`);
    console.log(`   Current time: ${now.toISOString()}`);
    console.log(`   Token expiry: ${expiry.toISOString()}`);
    console.log(`   Time remaining: ${Math.round((expiry - now) / (1000 * 60))} minutes`);

    if (expiry <= now) {
      console.log(`⏰ Expired token for user: ${user.email}`);
      return res.render('pages/auth/forgot-password', {
        title: 'Forgot Password',
        error: 'Your password reset link has expired. Please request a new password reset.',
        success: null
      });
    }

    // Token is valid and not expired
    const [users] = await db.query(
      'SELECT * FROM users WHERE reset_token = ? AND reset_token_expiry > NOW()',
      [token]
    );

    res.render('pages/auth/reset-password', {
      title: 'Reset Password',
      error: null,
      token: token
    });

  } catch (error) {
    console.error('Error in reset password page:', error);
    res.redirect('/auth/forgot-password');
  }
});

// Process password reset
router.post('/reset-password', async (req, res) => {
  try {
    const { token, password, confirmPassword } = req.body;

    // Validate input
    if (!token || !password || !confirmPassword) {
      return res.render('pages/auth/reset-password', {
        title: 'Reset Password',
        error: 'All fields are required',
        token: token
      });
    }

    if (password !== confirmPassword) {
      return res.render('pages/auth/reset-password', {
        title: 'Reset Password',
        error: 'Passwords do not match',
        token: token
      });
    }

    if (password.length < 6) {
      return res.render('pages/auth/reset-password', {
        title: 'Reset Password',
        error: 'Password must be at least 6 characters long',
        token: token
      });
    }

    // Check if token is valid and not expired (with detailed logging)
    const [tokenUsers] = await db.query(
      'SELECT *, reset_token_expiry FROM users WHERE reset_token = ?',
      [token]
    );

    if (tokenUsers.length === 0) {
      console.log(`❌ Invalid token in POST: ${token}`);
      return res.render('pages/auth/forgot-password', {
        title: 'Forgot Password',
        error: 'Invalid reset token. Please request a new password reset.',
        success: null
      });
    }

    const tokenUser = tokenUsers[0];
    const currentTime = new Date();
    const tokenExpiry = new Date(tokenUser.reset_token_expiry);

    console.log(`🔍 POST Token validation for user ${tokenUser.email}:`);
    console.log(`   Current time: ${currentTime.toISOString()}`);
    console.log(`   Token expiry: ${tokenExpiry.toISOString()}`);
    console.log(`   Time remaining: ${Math.round((tokenExpiry - currentTime) / (1000 * 60))} minutes`);

    if (tokenExpiry <= currentTime) {
      console.log(`⏰ Expired token in POST for user: ${tokenUser.email}`);
      return res.render('pages/auth/forgot-password', {
        title: 'Forgot Password',
        error: 'Your password reset link has expired. Please request a new password reset.',
        success: null
      });
    }

    // Final validation query
    const [users] = await db.query(
      'SELECT * FROM users WHERE reset_token = ? AND reset_token_expiry > NOW()',
      [token]
    );

    const user = users[0];

    // Hash new password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Update password and clear reset token
    await db.query(
      'UPDATE users SET password = ?, reset_token = NULL, reset_token_expiry = NULL WHERE id = ?',
      [hashedPassword, user.id]
    );

    // Redirect to login with success message
    res.render('pages/auth/login', {
      title: 'Login',
      error: null,
      success: 'Password reset successfully! Please log in with your new password.',
      redirect: '/'
    });

  } catch (error) {
    console.error('Error resetting password:', error);
    res.render('pages/auth/reset-password', {
      title: 'Reset Password',
      error: 'An error occurred. Please try again.',
      token: req.body.token
    });
  }
});

// Profile page
router.get('/profile', (req, res) => {
  if (!req.session.user) {
    return res.redirect('/auth/login?redirect=/auth/profile');
  }
  
  res.render('pages/auth/profile', { 
    title: 'My Profile',
    user: req.session.user
  });
});

module.exports = router;
