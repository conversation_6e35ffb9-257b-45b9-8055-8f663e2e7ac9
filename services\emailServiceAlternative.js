// Alternative Email Service with multiple providers and better deliverability
const nodemailer = require('nodemailer');

// Email service with fallback providers
const sendPasswordResetEmailAlternative = async (email, resetLink) => {
  console.log('🚀 Starting alternative email service...');
  
  // Try multiple email configurations
  const emailConfigs = [
    {
      name: 'Gmail SMTP (Primary)',
      config: {
        host: 'smtp.gmail.com',
        port: 587,
        secure: false,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        },
        tls: {
          rejectUnauthorized: false
        }
      }
    },
    {
      name: 'Gmail SMTP (Secure)',
      config: {
        host: 'smtp.gmail.com',
        port: 465,
        secure: true,
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      }
    }
  ];

  // Simple text version for better deliverability
  const simpleMailOptions = {
    from: process.env.EMAIL_USER,
    to: email,
    subject: 'Password Reset - Online Sports',
    text: `
Hello!

You requested a password reset for your Online Sports account.

Click this link to reset your password:
${resetLink}

This link will expire in 24 hours for security.

If you didn't request this, please ignore this email.

Best regards,
Online Sports Team
    `,
    html: `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Password Reset</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <h2 style="color: #333;">Password Reset Request</h2>
    
    <p>Hello!</p>
    
    <p>You requested a password reset for your Online Sports account.</p>
    
    <p>
        <a href="${resetLink}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block;">
            Reset My Password
        </a>
    </p>
    
    <p><strong>This link will expire in 24 hours for security.</strong></p>
    
    <p>If the button doesn't work, copy this link: <br>
    <code style="background: #f5f5f5; padding: 5px;">${resetLink}</code></p>
    
    <p>If you didn't request this, please ignore this email.</p>
    
    <hr>
    <p style="color: #666; font-size: 12px;">
        Online Sports Team<br>
        This email was sent to ${email}
    </p>
</body>
</html>
    `
  };

  // Try each configuration
  for (const emailConfig of emailConfigs) {
    try {
      console.log(`📧 Trying ${emailConfig.name}...`);
      
      const transporter = nodemailer.createTransporter(emailConfig.config);
      
      // Test connection
      await transporter.verify();
      console.log(`✅ ${emailConfig.name} connection verified`);
      
      // Send email
      const info = await transporter.sendMail(simpleMailOptions);
      
      console.log(`✅ Email sent successfully via ${emailConfig.name}!`);
      console.log(`📧 Email sent to: ${email}`);
      console.log(`📨 Message ID: ${info.messageId}`);
      console.log(`📬 Server Response: ${info.response}`);
      
      // Close transporter
      transporter.close();
      
      return { success: true, provider: emailConfig.name, messageId: info.messageId };
      
    } catch (error) {
      console.log(`❌ ${emailConfig.name} failed:`, error.message);
      continue;
    }
  }
  
  // If all providers fail, log the reset link
  console.log('='.repeat(60));
  console.log('❌ ALL EMAIL PROVIDERS FAILED');
  console.log('='.repeat(60));
  console.log('📧 PASSWORD RESET LINK (Use this manually):');
  console.log(`To: ${email}`);
  console.log(`Reset Link: ${resetLink}`);
  console.log('='.repeat(60));
  console.log('💡 TROUBLESHOOTING TIPS:');
  console.log('1. Check your Gmail App Password is correct');
  console.log('2. Ensure 2-Factor Authentication is enabled on Gmail');
  console.log('3. Try using a different email service (Brevo, Resend, etc.)');
  console.log('4. Check if your IP is blocked by Gmail');
  console.log('='.repeat(60));
  
  return { success: false, error: 'All email providers failed' };
};

// Test email function
const testEmailDelivery = async (testEmail) => {
  console.log('🧪 Testing email delivery...');
  
  const testLink = 'https://example.com/test-reset-link';
  const result = await sendPasswordResetEmailAlternative(testEmail, testLink);
  
  if (result.success) {
    console.log(`✅ Test email sent successfully via ${result.provider}`);
    console.log(`📨 Message ID: ${result.messageId}`);
  } else {
    console.log('❌ Test email failed:', result.error);
  }
  
  return result;
};

module.exports = {
  sendPasswordResetEmailAlternative,
  testEmailDelivery
};
