// Email Testing Script
require('dotenv').config();
const { testEmailDelivery } = require('./services/emailServiceAlternative');

async function runEmailTest() {
  console.log('🧪 EMAIL DELIVERY TEST SCRIPT');
  console.log('='.repeat(50));
  
  // Check environment variables
  console.log('📋 Checking email configuration...');
  console.log(`EMAIL_USER: ${process.env.EMAIL_USER ? '✅ Set' : '❌ Missing'}`);
  console.log(`EMAIL_PASS: ${process.env.EMAIL_PASS ? '✅ Set' : '❌ Missing'}`);
  console.log('');
  
  if (!process.env.EMAIL_USER || !process.env.EMAIL_PASS) {
    console.log('❌ Email credentials not configured!');
    console.log('💡 Add EMAIL_USER and EMAIL_PASS to your .env file');
    return;
  }
  
  // Test email address
  const testEmail = '<EMAIL>'; // Use the same email that didn't receive
  
  console.log(`📧 Testing email delivery to: ${testEmail}`);
  console.log('');
  
  try {
    const result = await testEmailDelivery(testEmail);
    
    if (result.success) {
      console.log('');
      console.log('🎉 EMAIL TEST COMPLETED SUCCESSFULLY!');
      console.log('📱 Check your email inbox and spam folder');
      console.log('🔍 Search for "Password Reset" or "Online Sports"');
    } else {
      console.log('');
      console.log('❌ EMAIL TEST FAILED');
      console.log('💡 Try the troubleshooting steps shown above');
    }
    
  } catch (error) {
    console.error('💥 Test script error:', error);
  }
}

// Run the test
runEmailTest();
