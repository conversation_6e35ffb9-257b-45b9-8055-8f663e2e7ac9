<div class="min-h-[80vh] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
  <!-- Background decorative elements -->
  <div class="absolute top-0 left-0 w-full h-full overflow-hidden z-0 opacity-50">
    <div class="absolute -top-24 -left-24 w-96 h-96 bg-accent-200 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob"></div>
    <div class="absolute top-[30%] -right-24 w-96 h-96 bg-primary-200 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-2000"></div>
    <div class="absolute -bottom-24 left-[30%] w-96 h-96 bg-accent-300 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-blob animation-delay-4000"></div>
  </div>

  <div class="max-w-md w-full relative z-10" data-aos="fade-up" data-aos-duration="1000">
    <!-- Logo and title -->
    <div class="text-center mb-8">
      <div class="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-br from-accent-500 to-accent-700 shadow-accent-glow mb-5 transform transition-all duration-500 hover:rotate-12 hover:scale-110">
        <i class="fas fa-user-plus text-white text-3xl"></i>
      </div>
      <h2 class="text-4xl font-bold font-heading text-secondary-900 mb-2">Join SportZone</h2>
      <p class="text-lg text-secondary-600">Create your account and start your sports journey</p>
    </div>

    <!-- Card with form -->
    <div class="bg-white/90 backdrop-blur-sm rounded-2xl shadow-strong border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-strong hover:scale-[1.01]">
      <div class="bg-gradient-to-r from-accent-500 to-accent-600 text-white py-5 px-6 relative overflow-hidden">
        <!-- Decorative pattern -->
        <div class="absolute inset-0 opacity-10">
          <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"%23ffffff\" fill-opacity=\"1\" fill-rule=\"evenodd\"%3E%3Ccircle cx=\"3\" cy=\"3\" r=\"1\"%3E%3C/circle%3E%3Ccircle cx=\"13\" cy=\"13\" r=\"1\"%3E%3C/circle%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>

        <h3 class="text-xl font-bold font-heading flex items-center relative z-10">
          <i class="fas fa-user-shield mr-2"></i> Create Your Account
        </h3>
      </div>

      <form action="/auth/register" method="POST" class="py-8 px-8">
        <input type="hidden" name="redirect" value="<%= locals.redirect || '/' %>">

        <% if (locals.error) { %>
          <div class="bg-red-100/80 backdrop-blur-sm border border-red-200 text-red-700 p-4 rounded-xl mb-6 animate-shake">
            <div class="flex items-center">
              <div class="flex-shrink-0 bg-red-100 rounded-full p-1">
                <i class="fas fa-exclamation-circle text-red-500"></i>
              </div>
              <p class="ml-3 text-sm font-medium"><%= error %></p>
            </div>
          </div>
        <% } %>

        <!-- Progress steps -->
        <div class="flex items-center justify-between mb-8">
          <div class="w-full">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center">
                <div class="w-6 h-6 rounded-full bg-accent-500 flex items-center justify-center text-white text-xs font-bold">1</div>
                <span class="ml-2 text-sm font-medium text-secondary-700">Account Details</span>
              </div>
              <div class="flex items-center">
                <div id="step2-indicator" class="w-6 h-6 rounded-full bg-secondary-200 flex items-center justify-center text-secondary-500 text-xs font-bold transition-all duration-300">2</div>
                <span class="ml-2 text-sm font-medium text-secondary-500">Security</span>
              </div>
            </div>
            <div class="overflow-hidden h-2 text-xs flex rounded-full bg-secondary-100">
              <div id="progress-bar" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-gradient-to-r from-accent-500 to-accent-600 w-1/2 transition-all duration-500 rounded-full"></div>
            </div>
          </div>
        </div>

        <!-- Step 1: Account details (visible by default) -->
        <div id="step-1" class="space-y-5">
          <div class="group">
            <label for="username" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Username</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-user text-secondary-400 group-focus-within:text-accent-500 transition-colors duration-200"></i>
              </div>
              <input type="text" id="username" name="username"
                    class="form-input w-full pl-10 pr-4 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-accent-500 focus:ring focus:ring-accent-200 focus:ring-opacity-50 transition-all duration-300"
                    required autocomplete="username">
            </div>
            <p class="mt-1.5 text-xs text-secondary-500 ml-1 flex items-center">
              <i class="fas fa-info-circle mr-1 text-accent-400"></i>
              Choose a unique username for your profile
            </p>
          </div>

          <div class="group">
            <label for="email" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Email Address</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-envelope text-secondary-400 group-focus-within:text-accent-500 transition-colors duration-200"></i>
              </div>
              <input type="email" id="email" name="email"
                    class="form-input w-full pl-10 pr-4 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-accent-500 focus:ring focus:ring-accent-200 focus:ring-opacity-50 transition-all duration-300"
                    required autocomplete="email">
            </div>
            <p class="mt-1.5 text-xs text-secondary-500 ml-1 flex items-center">
              <i class="fas fa-info-circle mr-1 text-accent-400"></i>
              We'll send a confirmation to this address
            </p>
          </div>

          <div class="group">
            <label for="fullName" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Full Name (Optional)</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-id-card text-secondary-400 group-focus-within:text-accent-500 transition-colors duration-200"></i>
              </div>
              <input type="text" id="fullName" name="fullName"
                    class="form-input w-full pl-10 pr-4 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-accent-500 focus:ring focus:ring-accent-200 focus:ring-opacity-50 transition-all duration-300"
                    autocomplete="name">
            </div>
          </div>

          <div class="pt-4">
            <button type="button" id="next-step" class="btn-accent w-full py-3 rounded-xl flex items-center justify-center group relative overflow-hidden shadow-lg">
              <span class="relative z-10 font-semibold">Continue</span>
              <i class="fas fa-arrow-right ml-2 relative z-10 transform group-hover:translate-x-1 transition-transform duration-200"></i>
              <div class="absolute inset-0 bg-gradient-to-r from-accent-500 to-accent-600 transition-all duration-300 group-hover:scale-105"></div>
            </button>
          </div>
        </div>

        <!-- Step 2: Security (hidden by default) -->
        <div id="step-2" class="space-y-5 hidden">
          <div class="group">
            <label for="password" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Password</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-lock text-secondary-400 group-focus-within:text-accent-500 transition-colors duration-200"></i>
              </div>
              <input type="password" id="password" name="password"
                    class="form-input w-full pl-10 pr-10 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-accent-500 focus:ring focus:ring-accent-200 focus:ring-opacity-50 transition-all duration-300"
                    required autocomplete="new-password">
              <button type="button" id="togglePassword" class="absolute inset-y-0 right-0 pr-3 flex items-center text-secondary-400 hover:text-secondary-600 transition-colors duration-200">
                <i class="fas fa-eye"></i>
              </button>
            </div>
            <div class="mt-2">
              <div class="password-strength-meter flex space-x-1.5 h-1.5 mt-1">
                <div class="w-1/4 h-full rounded-full bg-secondary-200 transition-colors duration-300" id="strength-1"></div>
                <div class="w-1/4 h-full rounded-full bg-secondary-200 transition-colors duration-300" id="strength-2"></div>
                <div class="w-1/4 h-full rounded-full bg-secondary-200 transition-colors duration-300" id="strength-3"></div>
                <div class="w-1/4 h-full rounded-full bg-secondary-200 transition-colors duration-300" id="strength-4"></div>
              </div>
              <p class="text-xs text-secondary-500 mt-1.5 ml-1 flex items-center" id="password-strength-text">
                <i class="fas fa-shield-alt mr-1 text-secondary-400"></i>
                Password strength
              </p>
            </div>
          </div>

          <div class="group">
            <label for="confirmPassword" class="form-label block text-sm font-medium text-secondary-700 mb-1 ml-1">Confirm Password</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-lock text-secondary-400 group-focus-within:text-accent-500 transition-colors duration-200"></i>
              </div>
              <input type="password" id="confirmPassword" name="confirmPassword"
                    class="form-input w-full pl-10 pr-4 py-3 rounded-xl border-secondary-200 bg-white/70 backdrop-blur-sm focus:border-accent-500 focus:ring focus:ring-accent-200 focus:ring-opacity-50 transition-all duration-300"
                    required autocomplete="new-password">
            </div>
            <p class="mt-1.5 text-xs text-secondary-500 ml-1 flex items-center" id="password-match-text">
              <i class="fas fa-info-circle mr-1 text-secondary-400"></i>
              Both passwords must match
            </p>
          </div>

          <div class="flex items-center mt-4 bg-secondary-50/50 p-3 rounded-xl">
            <input type="checkbox" id="terms" name="terms"
                  class="h-4 w-4 text-accent-600 border-secondary-300 rounded focus:ring-accent-500">
            <label for="terms" class="ml-2 text-sm text-secondary-600">
              I agree to the <a href="#" class="text-accent-600 hover:text-accent-700 hover:underline font-medium">Terms of Service</a> and <a href="#" class="text-accent-600 hover:text-accent-700 hover:underline font-medium">Privacy Policy</a>
            </label>
          </div>

          <div class="pt-4 flex space-x-3">
            <button type="button" id="prev-step" class="w-1/3 py-3 rounded-xl flex items-center justify-center group bg-white border border-secondary-200 text-secondary-700 hover:bg-secondary-50 transition-all duration-200 shadow-sm">
              <i class="fas fa-arrow-left mr-2 transform group-hover:-translate-x-1 transition-transform duration-200"></i>
              <span>Back</span>
            </button>
            <button type="submit" class="btn-accent w-2/3 py-3 rounded-xl flex items-center justify-center group relative overflow-hidden shadow-lg">
              <span class="relative z-10 font-semibold">Create Account</span>
              <i class="fas fa-check ml-2 relative z-10"></i>
              <div class="absolute inset-0 bg-gradient-to-r from-accent-500 to-accent-600 transition-all duration-300 group-hover:scale-105"></div>
            </button>
          </div>
        </div>

        <!-- Social registration options -->
        <div class="mt-8">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-secondary-200"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-4 bg-white text-secondary-500">Or sign up with</span>
            </div>
          </div>

          

        <div class="mt-8 text-center text-sm text-secondary-600">
          Already have an account?
          <a href="/auth/login<%= locals.redirect ? '?redirect=' + redirect : '' %>" class="text-accent-600 hover:text-accent-700 font-semibold transition-colors duration-200 hover:underline">
            Sign in
          </a>
        </div>
      </form>
    </div>

    <!-- Security note -->
    <div class="mt-6 text-center text-xs text-secondary-500 flex items-center justify-center bg-white/30 backdrop-blur-sm py-2 px-4 rounded-full shadow-sm">
      <i class="fas fa-shield-alt mr-2 text-accent-500"></i>
      <span>Secure, encrypted connection</span>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Multi-step form
    const step1 = document.getElementById('step-1');
    const step2 = document.getElementById('step-2');
    const nextBtn = document.getElementById('next-step');
    const prevBtn = document.getElementById('prev-step');
    const progressBar = document.getElementById('progress-bar');
    const step2Indicator = document.getElementById('step2-indicator');

    // Password elements
    const togglePassword = document.getElementById('togglePassword');
    const password = document.getElementById('password');
    const confirmPassword = document.getElementById('confirmPassword');
    const passwordStrengthText = document.getElementById('password-strength-text');
    const passwordMatchText = document.getElementById('password-match-text');
    const strengthBars = [
      document.getElementById('strength-1'),
      document.getElementById('strength-2'),
      document.getElementById('strength-3'),
      document.getElementById('strength-4')
    ];

    // Step navigation
    if (nextBtn && prevBtn) {
      nextBtn.addEventListener('click', function() {
        // Validate first step fields
        const username = document.getElementById('username');
        const email = document.getElementById('email');

        if (!username.value || !email.value) {
          // Show validation error with shake animation
          if (!username.value) {
            username.classList.add('border-red-500');
            username.classList.add('animate-shake');
            setTimeout(() => username.classList.remove('animate-shake'), 600);
          }
          if (!email.value) {
            email.classList.add('border-red-500');
            email.classList.add('animate-shake');
            setTimeout(() => email.classList.remove('animate-shake'), 600);
          }
          return;
        }

        // Move to step 2 with animation
        step1.classList.add('animate-slide-out-left');

        setTimeout(() => {
          step1.classList.add('hidden');
          step1.classList.remove('animate-slide-out-left');

          step2.classList.remove('hidden');
          step2.classList.add('animate-slide-in-right');

          progressBar.style.width = '100%';
          step2Indicator.classList.remove('bg-secondary-200', 'text-secondary-500');
          step2Indicator.classList.add('bg-accent-500', 'text-white');

          setTimeout(() => {
            step2.classList.remove('animate-slide-in-right');
          }, 500);
        }, 300);
      });

      prevBtn.addEventListener('click', function() {
        // Move back to step 1 with animation
        step2.classList.add('animate-slide-out-right');

        setTimeout(() => {
          step2.classList.add('hidden');
          step2.classList.remove('animate-slide-out-right');

          step1.classList.remove('hidden');
          step1.classList.add('animate-slide-in-left');

          progressBar.style.width = '50%';
          step2Indicator.classList.add('bg-secondary-200', 'text-secondary-500');
          step2Indicator.classList.remove('bg-accent-500', 'text-white');

          setTimeout(() => {
            step1.classList.remove('animate-slide-in-left');
          }, 500);
        }, 300);
      });
    }

    // Password visibility toggle
    if (togglePassword && password) {
      togglePassword.addEventListener('click', function() {
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);

        // Toggle icon with animation
        const icon = this.querySelector('i');
        icon.classList.add('animate-flip');
        setTimeout(() => {
          icon.classList.toggle('fa-eye');
          icon.classList.toggle('fa-eye-slash');
          icon.classList.remove('animate-flip');
        }, 150);
      });
    }

    // Password strength meter
    if (password && strengthBars.length === 4) {
      password.addEventListener('input', function() {
        const value = this.value;
        let strength = 0;

        // Reset all bars
        strengthBars.forEach(bar => {
          bar.classList.remove('bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500');
          bar.classList.add('bg-secondary-200');
        });

        // Check password strength
        if (value.length > 0) strength += 1;
        if (value.length >= 8) strength += 1;
        if (/[A-Z]/.test(value) && /[a-z]/.test(value)) strength += 1;
        if (/[0-9]/.test(value) || /[^A-Za-z0-9]/.test(value)) strength += 1;

        // Update strength bars with animation
        for (let i = 0; i < strength; i++) {
          setTimeout(() => {
            if (strength === 1) {
              strengthBars[i].classList.add('bg-red-500', 'animate-pulse-once');
              passwordStrengthText.innerHTML = '<i class="fas fa-shield-alt mr-1"></i> Weak password';
              passwordStrengthText.classList.add('text-red-500');
              passwordStrengthText.classList.remove('text-orange-500', 'text-yellow-500', 'text-green-500', 'text-secondary-500');
            } else if (strength === 2) {
              strengthBars[i].classList.add('bg-orange-500', 'animate-pulse-once');
              passwordStrengthText.innerHTML = '<i class="fas fa-shield-alt mr-1"></i> Fair password';
              passwordStrengthText.classList.add('text-orange-500');
              passwordStrengthText.classList.remove('text-red-500', 'text-yellow-500', 'text-green-500', 'text-secondary-500');
            } else if (strength === 3) {
              strengthBars[i].classList.add('bg-yellow-500', 'animate-pulse-once');
              passwordStrengthText.innerHTML = '<i class="fas fa-shield-alt mr-1"></i> Good password';
              passwordStrengthText.classList.add('text-yellow-500');
              passwordStrengthText.classList.remove('text-red-500', 'text-orange-500', 'text-green-500', 'text-secondary-500');
            } else if (strength === 4) {
              strengthBars[i].classList.add('bg-green-500', 'animate-pulse-once');
              passwordStrengthText.innerHTML = '<i class="fas fa-shield-alt mr-1"></i> Strong password';
              passwordStrengthText.classList.add('text-green-500');
              passwordStrengthText.classList.remove('text-red-500', 'text-orange-500', 'text-yellow-500', 'text-secondary-500');
            }

            setTimeout(() => {
              strengthBars[i].classList.remove('animate-pulse-once');
            }, 500);
          }, i * 100);
        }
      });
    }

    // Password match validation
    if (password && confirmPassword && passwordMatchText) {
      function checkPasswordMatch() {
        if (confirmPassword.value && password.value !== confirmPassword.value) {
          passwordMatchText.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Passwords do not match';
          passwordMatchText.classList.add('text-red-500');
          passwordMatchText.classList.remove('text-green-500', 'text-secondary-500');
          confirmPassword.classList.add('border-red-500');
          confirmPassword.classList.remove('border-green-500');
        } else if (confirmPassword.value && password.value === confirmPassword.value) {
          passwordMatchText.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Passwords match';
          passwordMatchText.classList.add('text-green-500');
          passwordMatchText.classList.remove('text-red-500', 'text-secondary-500');
          confirmPassword.classList.add('border-green-500');
          confirmPassword.classList.remove('border-red-500');
        } else {
          passwordMatchText.innerHTML = '<i class="fas fa-info-circle mr-1"></i> Both passwords must match';
          passwordMatchText.classList.remove('text-red-500', 'text-green-500');
          passwordMatchText.classList.add('text-secondary-500');
          confirmPassword.classList.remove('border-red-500', 'border-green-500');
        }
      }

      password.addEventListener('input', checkPasswordMatch);
      confirmPassword.addEventListener('input', checkPasswordMatch);
    }

    // Input focus effects
    const inputs = document.querySelectorAll('.form-input');
    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentNode.parentNode.classList.add('focused');
        this.classList.add('input-focused');
        this.classList.remove('border-red-500');
      });

      input.addEventListener('blur', function() {
        if (!this.value) {
          this.parentNode.parentNode.classList.remove('focused');
        }
        this.classList.remove('input-focused');
      });

      // Check if input has value on page load
      if (input.value) {
        input.parentNode.parentNode.classList.add('focused');
      }
    });

    // Subtle background animation
    const animateBackground = () => {
      const blobs = document.querySelectorAll('.animate-blob');
      blobs.forEach(blob => {
        const randomX = Math.random() * 10 - 5;
        const randomY = Math.random() * 10 - 5;
        blob.style.transform = `translate(${randomX}px, ${randomY}px)`;
        setTimeout(() => {
          blob.style.transition = 'transform 15s ease-in-out';
        }, 100);
      });
    };

    // Run animation every 15 seconds
    animateBackground();
    setInterval(animateBackground, 15000);
  });
</script>

<style>
  .focused .form-label {
    color: theme('colors.accent.600');
    transform: translateY(-0.5rem) scale(0.9);
    transform-origin: left top;
  }

  .form-label {
    transition: all 0.2s ease-out;
  }

  .group:focus-within .fas {
    color: theme('colors.accent.500');
  }

  .input-focused {
    border-color: theme('colors.accent.500') !important;
    box-shadow: 0 0 0 3px theme('colors.accent.100') !important;
  }

  /* Animation classes */
  .animate-blob {
    animation: blob-bounce 7s infinite;
  }

  .animation-delay-2000 {
    animation-delay: 2s;
  }

  .animation-delay-4000 {
    animation-delay: 4s;
  }

  @keyframes blob-bounce {
    0% {
      transform: translate(0px, 0px) scale(1);
    }
    33% {
      transform: translate(20px, -20px) scale(1.1);
    }
    66% {
      transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
      transform: translate(0px, 0px) scale(1);
    }
  }

  @keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
  }

  .animate-shake {
    animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  .animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  @keyframes slideInRight {
    from { transform: translateX(100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  @keyframes slideOutLeft {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(-100%); opacity: 0; }
  }

  .animate-slide-out-left {
    animation: slideOutLeft 0.3s ease-out;
  }

  @keyframes slideInLeft {
    from { transform: translateX(-100%); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  @keyframes slideOutRight {
    from { transform: translateX(0); opacity: 1; }
    to { transform: translateX(100%); opacity: 0; }
  }

  .animate-slide-out-right {
    animation: slideOutRight 0.3s ease-out;
  }

  @keyframes flip {
    0% { transform: rotateY(0); }
    50% { transform: rotateY(90deg); }
    100% { transform: rotateY(0); }
  }

  .animate-flip {
    animation: flip 0.3s ease;
  }

  @keyframes pulseOnce {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
  }

  .animate-pulse-once {
    animation: pulseOnce 0.5s ease-in-out;
  }
</style>
